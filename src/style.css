* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  background-color: #ffffff;
  color: #333333;
}

#app {
  max-width: 100%;
  margin: 0 auto;
}

.spreadsheet-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cell-editor {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.cell-editor label {
  font-weight: 500;
  min-width: 100px;
}

.cell-editor input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
}

.cell-editor input:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.spreadsheet-grid {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: auto;
  background-color: white;
}

.grid-table {
  border-collapse: collapse;
  width: 100%;
  min-width: 800px;
}

.grid-table th,
.grid-table td {
  border: 1px solid #ddd;
  padding: 0;
  margin: 0;
  width: 80px;
  height: 30px;
  text-align: center;
  vertical-align: middle;
}

.grid-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 12px;
  color: #666;
}

.grid-table td {
  position: relative;
}

.cell {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  padding: 4px 6px;
  font-family: inherit;
  font-size: 14px;
  text-align: left;
  cursor: cell;
}

.cell:focus {
  outline: 2px solid #007acc;
  outline-offset: -2px;
  background-color: #f0f8ff;
}

.cell.selected {
  background-color: #e6f3ff;
}

.row-header {
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 12px;
  color: #666;
  width: 40px;
}
