* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  background-color: #ffffff;
  color: #333333;
}

#app {
  max-width: 100%;
  margin: 0 auto;
}

.spreadsheet-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.file-operations {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.file-operations button {
  padding: 8px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  color: #333;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: background-color 0.2s;
}

.file-operations button:hover {
  background-color: #f0f0f0;
}

.file-operations button:active {
  background-color: #e0e0e0;
}

.cell-editor {
  display: flex;
  align-items: center;
}

.cell-editor input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
}

.cell-editor input:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.spreadsheet-grid {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: auto;
  background-color: white;
  height: 600px;
}

.grid-container {
  display: grid;
  width: fit-content;
  height: fit-content;
  min-width: 100%;
  min-height: 100%;
  position: relative;
  gap: 0;
}

.grid-cell {
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  background-color: white;
  position: relative;
}

.grid-header {
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  z-index: 10;
}

.corner-header {
  grid-row: 1;
  grid-column: 1;
  top: 0;
  left: 0;
  z-index: 12;
  border-top: 1px solid #ddd;
  border-left: 1px solid #ddd;
}

.column-header {
  grid-row: 1;
  top: 0;
  z-index: 11;
  border-top: 1px solid #ddd;
}

.row-header {
  grid-column: 1;
  left: 0;
  z-index: 11;
  border-left: 1px solid #ddd;
}

.data-cell {
  /* Grid position will be set dynamically */
}

/* Column resize handle */
.column-header::after {
  content: '';
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 10;
}

.column-header:hover::after {
  background: rgba(0, 122, 204, 0.3);
}

/* Row resize handle */
.row-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 4px;
  cursor: row-resize;
  background: transparent;
  z-index: 10;
}

.row-header:hover::after {
  background: rgba(0, 122, 204, 0.3);
}

/* Resize indicator line */
.resize-line {
  position: absolute;
  background: #007acc;
  z-index: 1000;
  pointer-events: none;
}

.resize-line.vertical {
  width: 2px;
  top: 0;
  bottom: 0;
}

.resize-line.horizontal {
  height: 2px;
  left: 0;
  right: 0;
}

.cell {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  padding: 4px 6px;
  font-family: inherit;
  font-size: 14px;
  text-align: left;
  cursor: cell;
  position: absolute;
  top: 0;
  left: 0;
}

.cell:focus {
  outline: 2px solid #007acc;
  outline-offset: -2px;
  background-color: #f0f8ff;
}

.cell.selected {
  background-color: #e6f3ff;
}

.cell.multi-selected {
  background-color: #d4edda;
}

.cell.selection-start {
  background-color: #e6f3ff;
  outline: 2px solid #007acc;
  outline-offset: -2px;
}

.selection-overlay {
  position: absolute;
  border: 2px solid #007acc;
  background-color: rgba(0, 122, 204, 0.1);
  pointer-events: none;
  z-index: 5;
}

.cell.copied {
  border: 2px dashed #28a745;
  border-radius: 2px;
}

.copied-overlay {
  position: absolute;
  border: 2px dashed #28a745;
  background-color: rgba(40, 167, 69, 0.1);
  pointer-events: none;
  z-index: 4;
}
