* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  background-color: #ffffff;
  color: #333333;
}

#app {
  max-width: 100%;
  margin: 0 auto;
}

.spreadsheet-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.file-operations {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.file-operations button {
  padding: 8px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  color: #333;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: background-color 0.2s;
}

.file-operations button:hover {
  background-color: #f0f0f0;
}

.file-operations button:active {
  background-color: #e0e0e0;
}

.cell-editor {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cell-editor label {
  font-weight: 500;
  min-width: 100px;
}

.cell-editor input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
}

.cell-editor input:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.spreadsheet-grid {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: auto;
  background-color: white;
  position: relative;
  height: 600px;
}

.grid-container {
  position: relative;
  width: fit-content;
  height: fit-content;
  min-width: 100%;
  min-height: 100%;
}

.grid-cell,
.grid-header {
  position: absolute;
  border: 1px solid #ddd;
  box-sizing: border-box;
  background-color: white;
}

.grid-header {
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.grid-cell {
  z-index: 1;
}

.corner-header {
  top: 0;
  left: 0;
  width: 40px;
  height: 30px;
  z-index: 3;
}

.column-header {
  top: 0;
  height: 30px;
  width: 80px;
}

.row-header {
  left: 0;
  width: 40px;
  height: 30px;
}

.data-cell {
  width: 80px;
  height: 30px;
}

/* Column resize handle */
.column-header::after {
  content: '';
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 10;
}

.column-header:hover::after {
  background: rgba(0, 122, 204, 0.3);
}

/* Row resize handle */
.row-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 4px;
  cursor: row-resize;
  background: transparent;
  z-index: 10;
}

.row-header:hover::after {
  background: rgba(0, 122, 204, 0.3);
}

/* Resize indicator line */
.resize-line {
  position: absolute;
  background: #007acc;
  z-index: 1000;
  pointer-events: none;
}

.resize-line.vertical {
  width: 2px;
  top: 0;
  bottom: 0;
}

.resize-line.horizontal {
  height: 2px;
  left: 0;
  right: 0;
}

.cell {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  padding: 4px 6px;
  font-family: inherit;
  font-size: 14px;
  text-align: left;
  cursor: cell;
  position: absolute;
  top: 0;
  left: 0;
}

.cell:focus {
  outline: 2px solid #007acc;
  outline-offset: -2px;
  background-color: #f0f8ff;
}

.cell.selected {
  background-color: #e6f3ff;
}
