import './style.css'

class SimpleSpreadsheet {
  constructor() {
    this.data = {};
    this.currentCell = null;
    this.rows = 20;
    this.cols = 10;
    this.columnWidths = {};
    this.rowHeights = {};

    // Selection properties
    this.selectedCells = new Set();
    this.isSelecting = false;
    this.selectionStart = null;
    this.selectionEnd = null;

    // Copy/paste properties
    this.copiedData = null;
    this.copiedRange = null;

    // Persistence properties
    this.storageKey = 'simple-spreadsheet-data';
    this.autoSaveEnabled = true;

    this.init();
  }

  init() {
    this.loadFromLocalStorage();
    this.createGrid();
    this.setupEventListeners();
    this.setupResizeHandlers();
    this.populateGridFromData();
  }

  createGrid() {
    const gridContainer = document.getElementById('spreadsheet-grid');
    const container = document.createElement('div');
    container.className = 'grid-container';

    // Default dimensions
    this.defaultColumnWidth = 80;
    this.defaultRowHeight = 30;
    this.headerHeight = 30;
    this.headerWidth = 40;

    // Set up CSS Grid template
    this.updateGridTemplate(container);

    // Create corner header
    const cornerHeader = document.createElement('div');
    cornerHeader.className = 'grid-header corner-header';
    container.appendChild(cornerHeader);

    // Create column headers
    for (let col = 0; col < this.cols; col++) {
      const colHeader = document.createElement('div');
      colHeader.className = 'grid-header column-header';
      colHeader.textContent = String.fromCharCode(65 + col);
      colHeader.dataset.col = col;
      colHeader.style.gridColumn = col + 2; // +2 because grid is 1-indexed and we have row header
      container.appendChild(colHeader);
    }

    // Create row headers and data cells
    for (let row = 0; row < this.rows; row++) {
      // Row header
      const rowHeader = document.createElement('div');
      rowHeader.className = 'grid-header row-header';
      rowHeader.textContent = (row + 1).toString();
      rowHeader.dataset.row = row;
      rowHeader.style.gridRow = row + 2; // +2 because grid is 1-indexed and we have column header
      container.appendChild(rowHeader);

      // Data cells
      for (let col = 0; col < this.cols; col++) {
        const cellContainer = document.createElement('div');
        cellContainer.className = 'grid-cell data-cell';
        cellContainer.style.gridRow = row + 2;
        cellContainer.style.gridColumn = col + 2;

        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'cell';
        input.dataset.row = row;
        input.dataset.col = col;
        input.dataset.cellId = this.getCellId(row, col);

        cellContainer.appendChild(input);
        container.appendChild(cellContainer);
      }
    }

    gridContainer.appendChild(container);
  }

  updateGridTemplate(container) {
    // Build grid template columns: header + data columns
    const columnSizes = [this.headerWidth + 'px'];
    for (let col = 0; col < this.cols; col++) {
      const colLetter = String.fromCharCode(65 + col);
      const width = this.columnWidths[colLetter] || this.defaultColumnWidth;
      columnSizes.push(width + 'px');
    }

    // Build grid template rows: header + data rows
    const rowSizes = [this.headerHeight + 'px'];
    for (let row = 0; row < this.rows; row++) {
      const height = this.rowHeights[row + 1] || this.defaultRowHeight;
      rowSizes.push(height + 'px');
    }

    container.style.gridTemplateColumns = columnSizes.join(' ');
    container.style.gridTemplateRows = rowSizes.join(' ');
  }

  getCellId(row, col) {
    return String.fromCharCode(65 + col) + (row + 1);
  }

  setupEventListeners() {
    const cellInput = document.getElementById('cell-input');
    const cells = document.querySelectorAll('.cell');

    // Handle cell focus and selection
    cells.forEach(cell => {
      cell.addEventListener('focus', (e) => {
        this.selectCell(e.target);
      });

      cell.addEventListener('input', (e) => {
        this.updateCellData(e.target);
      });

      cell.addEventListener('keydown', (e) => {
        this.handleKeyNavigation(e);
      });

      // Add drag selection handlers
      cell.addEventListener('mousedown', (e) => {
        this.handleSelectionStart(e, cell);
      });

      cell.addEventListener('mouseenter', (e) => {
        this.handleSelectionMove(e, cell);
      });
    });

    // Handle cell editor input
    cellInput.addEventListener('input', (e) => {
      if (this.currentCell) {
        this.currentCell.value = e.target.value;
        this.updateCellData(this.currentCell);
      }
    });

    cellInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && this.currentCell) {
        this.currentCell.focus();
      }
    });

    // File operation event listeners
    this.setupFileOperations();

    // Global mouse events for selection
    document.addEventListener('mouseup', (e) => {
      this.handleSelectionEnd(e);
    });

    // Global keyboard events for copy/paste
    document.addEventListener('keydown', (e) => {
      this.handleGlobalKeydown(e);
    });
  }

  setupFileOperations() {
    const saveCsvBtn = document.getElementById('save-csv');
    const loadCsvBtn = document.getElementById('load-csv');
    const saveJsonBtn = document.getElementById('save-json');
    const loadJsonBtn = document.getElementById('load-json');
    const fileInput = document.getElementById('file-input');

    saveCsvBtn.addEventListener('click', () => this.saveAsCSV());
    saveJsonBtn.addEventListener('click', () => this.saveAsJSON());

    loadCsvBtn.addEventListener('click', () => {
      fileInput.accept = '.csv';
      fileInput.onchange = (e) => this.loadCSV(e);
      fileInput.click();
    });

    loadJsonBtn.addEventListener('click', () => {
      fileInput.accept = '.json';
      fileInput.onchange = (e) => this.loadJSON(e);
      fileInput.click();
    });
  }

  selectCell(cell) {
    // Clear multi-selection when focusing on a single cell
    this.clearSelection();

    // Remove previous single selection
    document.querySelectorAll('.cell.selected').forEach(c => {
      c.classList.remove('selected');
    });

    // Select new cell
    cell.classList.add('selected');
    this.currentCell = cell;

    // Update cell editor
    const cellInput = document.getElementById('cell-input');
    cellInput.value = cell.value;
    cellInput.placeholder = `Editing ${cell.dataset.cellId}`;
  }

  updateCellData(cell) {
    const cellId = cell.dataset.cellId;
    this.data[cellId] = cell.value;
    this.saveToLocalStorage();
  }

  handleKeyNavigation(e) {
    if (!this.currentCell) return;

    const row = parseInt(this.currentCell.dataset.row);
    const col = parseInt(this.currentCell.dataset.col);
    let newRow = row;
    let newCol = col;

    switch (e.key) {
      case 'ArrowUp':
        newRow = Math.max(0, row - 1);
        e.preventDefault();
        break;
      case 'ArrowDown':
        newRow = Math.min(this.rows - 1, row + 1);
        e.preventDefault();
        break;
      case 'ArrowLeft':
        newCol = Math.max(0, col - 1);
        e.preventDefault();
        break;
      case 'ArrowRight':
        newCol = Math.min(this.cols - 1, col + 1);
        e.preventDefault();
        break;
      case 'Enter':
        newRow = Math.min(this.rows - 1, row + 1);
        e.preventDefault();
        break;
      case 'Tab':
        newCol = Math.min(this.cols - 1, col + 1);
        e.preventDefault();
        break;
    }

    if (newRow !== row || newCol !== col) {
      const newCell = document.querySelector(`[data-row="${newRow}"][data-col="${newCol}"]`);
      if (newCell) {
        newCell.focus();
      }
    }
  }

  saveAsCSV() {
    const csvData = [];

    // Find the maximum row and column with data
    let maxRow = 0;
    let maxCol = 0;

    Object.keys(this.data).forEach(cellId => {
      if (this.data[cellId]) {
        const col = cellId.charCodeAt(0) - 65;
        const row = parseInt(cellId.substring(1)) - 1;
        maxRow = Math.max(maxRow, row);
        maxCol = Math.max(maxCol, col);
      }
    });

    // Generate CSV rows
    for (let row = 0; row <= maxRow; row++) {
      const csvRow = [];
      for (let col = 0; col <= maxCol; col++) {
        const cellId = this.getCellId(row, col);
        const value = this.data[cellId] || '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          csvRow.push('"' + value.replace(/"/g, '""') + '"');
        } else {
          csvRow.push(value);
        }
      }
      csvData.push(csvRow.join(','));
    }

    const csvContent = csvData.join('\n');
    this.downloadFile(csvContent, 'spreadsheet.csv', 'text/csv');
  }

  saveAsJSON() {
    const jsonData = {
      version: '1.0',
      data: this.data,
      columnWidths: this.columnWidths,
      rowHeights: this.rowHeights,
      metadata: {
        rows: this.rows,
        cols: this.cols,
        savedAt: new Date().toISOString()
      }
    };

    const jsonContent = JSON.stringify(jsonData, null, 2);
    this.downloadFile(jsonContent, 'spreadsheet.json', 'application/json');
  }

  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  loadCSV(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const csvContent = e.target.result;
      this.parseCSV(csvContent);
    };
    reader.readAsText(file);
  }

  parseCSV(csvContent) {
    // Clear existing data
    this.clearAllData();

    const lines = csvContent.split('\n');

    lines.forEach((line, rowIndex) => {
      if (line.trim() === '') return;

      const values = this.parseCSVLine(line);
      values.forEach((value, colIndex) => {
        if (value.trim() !== '') {
          const cellId = this.getCellId(rowIndex, colIndex);
          this.data[cellId] = value;

          // Update the cell in the UI
          const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
          if (cell) {
            cell.value = value;
          }
        }
      });
    });

    // Save loaded CSV data to localStorage
    this.saveToLocalStorage();
  }

  parseCSVLine(line) {
    const values = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        values.push(current);
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    values.push(current);
    return values;
  }

  loadJSON(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result);
        this.parseJSON(jsonData);
      } catch (error) {
        alert('Error loading JSON file: ' + error.message);
      }
    };
    reader.readAsText(file);
  }

  parseJSON(jsonData) {
    // Clear existing data
    this.clearAllData();

    // Load data
    if (jsonData.data) {
      this.data = { ...jsonData.data };
    }

    // Load layout information
    if (jsonData.columnWidths) {
      this.columnWidths = { ...jsonData.columnWidths };
    }

    if (jsonData.rowHeights) {
      this.rowHeights = { ...jsonData.rowHeights };
    }

    // Update UI with loaded data
    Object.keys(this.data).forEach(cellId => {
      const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
      if (cell) {
        cell.value = this.data[cellId];
      }
    });

    // Apply column widths and row heights
    this.applyLayoutSettings();

    // Save loaded data to localStorage
    this.saveToLocalStorage();
  }

  clearAllData() {
    this.data = {};
    this.columnWidths = {};
    this.rowHeights = {};

    // Clear all cells in the UI
    document.querySelectorAll('.cell').forEach(cell => {
      cell.value = '';
    });

    // Save cleared state to localStorage
    this.saveToLocalStorage();
  }

  applyLayoutSettings() {
    // Simply update the grid template with the loaded sizes
    const container = document.querySelector('.grid-container');
    if (container) {
      this.updateGridTemplate(container);
    }
  }

  setupResizeHandlers() {
    this.isResizing = false;
    this.resizeType = null; // 'column' or 'row'
    this.resizeIndex = -1;
    this.startPos = 0;
    this.startSize = 0;
    this.resizeLine = null;

    // Add event listeners for column headers
    const columnHeaders = document.querySelectorAll('.column-header');
    columnHeaders.forEach((header) => {
      const colIndex = parseInt(header.dataset.col);
      header.addEventListener('mousedown', (e) => this.handleColumnResizeStart(e, colIndex));
    });

    // Add event listeners for row headers
    const rowHeaders = document.querySelectorAll('.row-header');
    rowHeaders.forEach((header) => {
      const rowIndex = parseInt(header.dataset.row);
      header.addEventListener('mousedown', (e) => this.handleRowResizeStart(e, rowIndex));
    });

    // Global mouse events
    document.addEventListener('mousemove', (e) => this.handleResizeMove(e));
    document.addEventListener('mouseup', (e) => this.handleResizeEnd(e));
  }

  handleColumnResizeStart(e, colIndex) {
    // Check if click is near the right edge
    const rect = e.target.getBoundingClientRect();
    const clickX = e.clientX - rect.left;

    if (clickX > rect.width - 8) { // 8px tolerance for resize area
      e.preventDefault();

      // Check for double-click to auto-resize
      if (e.detail === 2) {
        this.autoResizeColumn(colIndex);
        return;
      }

      this.isResizing = true;
      this.resizeType = 'column';
      this.resizeIndex = colIndex;
      this.startPos = e.clientX;
      this.startSize = rect.width;

      this.createResizeLine('vertical', e.clientX);
      document.body.style.cursor = 'col-resize';
    }
  }

  handleRowResizeStart(e, rowIndex) {
    // Check if click is near the bottom edge
    const rect = e.target.getBoundingClientRect();
    const clickY = e.clientY - rect.top;

    if (clickY > rect.height - 8) { // 8px tolerance for resize area
      e.preventDefault();
      this.isResizing = true;
      this.resizeType = 'row';
      this.resizeIndex = rowIndex;
      this.startPos = e.clientY;
      this.startSize = rect.height;

      this.createResizeLine('horizontal', e.clientY);
      document.body.style.cursor = 'row-resize';
    }
  }

  createResizeLine(type, position) {
    this.resizeLine = document.createElement('div');
    this.resizeLine.className = `resize-line ${type}`;

    if (type === 'vertical') {
      this.resizeLine.style.left = position + 'px';
    } else {
      this.resizeLine.style.top = position + 'px';
    }

    document.body.appendChild(this.resizeLine);
  }

  handleResizeMove(e) {
    if (!this.isResizing || !this.resizeLine) return;

    e.preventDefault();

    if (this.resizeType === 'column') {
      const newX = e.clientX;
      this.resizeLine.style.left = newX + 'px';
    } else if (this.resizeType === 'row') {
      const newY = e.clientY;
      this.resizeLine.style.top = newY + 'px';
    }
  }

  handleResizeEnd(e) {
    if (!this.isResizing) return;

    e.preventDefault();

    if (this.resizeType === 'column') {
      const deltaX = e.clientX - this.startPos;
      const newWidth = Math.max(30, this.startSize + deltaX); // Minimum width of 30px
      this.setColumnWidth(this.resizeIndex, newWidth);

      // Store in columnWidths for saving
      const colLetter = String.fromCharCode(65 + this.resizeIndex);
      this.columnWidths[colLetter] = newWidth;

    } else if (this.resizeType === 'row') {
      const deltaY = e.clientY - this.startPos;
      const newHeight = Math.max(20, this.startSize + deltaY); // Minimum height of 20px
      this.setRowHeight(this.resizeIndex, newHeight);

      // Store in rowHeights for saving
      this.rowHeights[this.resizeIndex + 1] = newHeight;
    }

    // Clean up
    if (this.resizeLine) {
      document.body.removeChild(this.resizeLine);
      this.resizeLine = null;
    }

    document.body.style.cursor = '';
    this.isResizing = false;
    this.resizeType = null;
    this.resizeIndex = -1;
  }

  setColumnWidth(colIndex, width) {
    // Store the new width
    const colLetter = String.fromCharCode(65 + colIndex);
    this.columnWidths[colLetter] = width;

    // Update the grid template
    const container = document.querySelector('.grid-container');
    if (container) {
      this.updateGridTemplate(container);
    }

    // Save to localStorage
    this.saveToLocalStorage();
  }

  setRowHeight(rowIndex, height) {
    // Store the new height
    this.rowHeights[rowIndex + 1] = height;

    // Update the grid template
    const container = document.querySelector('.grid-container');
    if (container) {
      this.updateGridTemplate(container);
    }

    // Save to localStorage
    this.saveToLocalStorage();
  }

  autoResizeColumn(colIndex) {
    // Create a temporary canvas to measure text width
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif';

    let maxWidth = 0;
    const padding = 12; // 6px padding on each side
    const minWidth = 30; // Minimum column width

    // Check all cells in this column
    for (let row = 0; row < this.rows; row++) {
      const cellId = this.getCellId(row, colIndex);
      const cellValue = this.data[cellId] || '';

      if (cellValue) {
        const textWidth = ctx.measureText(cellValue).width;
        maxWidth = Math.max(maxWidth, textWidth + padding);
      }
    }

    // Also consider the header text
    const headerText = String.fromCharCode(65 + colIndex);
    const headerWidth = ctx.measureText(headerText).width + padding;
    maxWidth = Math.max(maxWidth, headerWidth);

    // Set minimum width if no content
    if (maxWidth === 0) {
      maxWidth = this.defaultColumnWidth;
    }

    // Ensure minimum width
    maxWidth = Math.max(maxWidth, minWidth);

    // Apply the new width
    this.setColumnWidth(colIndex, Math.ceil(maxWidth));
  }

  handleSelectionStart(e, cell) {
    // Don't interfere with resize operations
    if (this.isResizing) return;

    // Only start selection on left mouse button
    if (e.button !== 0) return;

    e.preventDefault();

    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);

    // Clear previous selection unless Ctrl/Cmd is held
    if (!e.ctrlKey && !e.metaKey) {
      this.clearSelection();
    }

    this.isSelecting = true;
    this.selectionStart = { row, col };
    this.selectionEnd = { row, col };

    this.updateSelection();
  }

  handleSelectionMove(e, cell) {
    if (!this.isSelecting) return;

    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);

    this.selectionEnd = { row, col };
    this.updateSelection();
  }

  handleSelectionEnd(e) {
    if (this.isSelecting) {
      this.isSelecting = false;
      // Keep the selection visible
    }
  }

  clearSelection() {
    // Remove selection classes from all cells
    document.querySelectorAll('.cell').forEach(cell => {
      cell.classList.remove('multi-selected', 'selection-start');
    });

    this.selectedCells.clear();

    // Remove selection overlay if it exists
    const overlay = document.querySelector('.selection-overlay');
    if (overlay) {
      overlay.remove();
    }
  }

  updateSelection() {
    if (!this.selectionStart || !this.selectionEnd) return;

    this.clearSelection();

    const startRow = Math.min(this.selectionStart.row, this.selectionEnd.row);
    const endRow = Math.max(this.selectionStart.row, this.selectionEnd.row);
    const startCol = Math.min(this.selectionStart.col, this.selectionEnd.col);
    const endCol = Math.max(this.selectionStart.col, this.selectionEnd.col);

    // Select all cells in the range
    for (let row = startRow; row <= endRow; row++) {
      for (let col = startCol; col <= endCol; col++) {
        const cellId = this.getCellId(row, col);
        this.selectedCells.add(cellId);

        const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
        if (cell) {
          if (row === this.selectionStart.row && col === this.selectionStart.col) {
            cell.classList.add('selection-start');
          } else {
            cell.classList.add('multi-selected');
          }
        }
      }
    }

    // Create visual selection overlay
    this.createSelectionOverlay(startRow, startCol, endRow, endCol);
  }

  createSelectionOverlay(startRow, startCol, endRow, endCol) {
    // Remove existing overlay
    const existingOverlay = document.querySelector('.selection-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }

    // Get the grid container and spreadsheet grid for positioning
    const gridContainer = document.querySelector('.grid-container');
    const spreadsheetGrid = document.querySelector('.spreadsheet-grid');
    if (!gridContainer || !spreadsheetGrid) return;

    const overlay = document.createElement('div');
    overlay.className = 'selection-overlay';

    // Get the actual cell containers
    const startCellContainer = document.querySelector(`[data-row="${startRow}"][data-col="${startCol}"]`).parentElement;
    const endCellContainer = document.querySelector(`[data-row="${endRow}"][data-col="${endCol}"]`).parentElement;

    if (startCellContainer && endCellContainer) {
      // Calculate positions relative to the grid container
      const gridRect = gridContainer.getBoundingClientRect();
      const startRect = startCellContainer.getBoundingClientRect();
      const endRect = endCellContainer.getBoundingClientRect();

      // Position relative to grid container
      const left = startRect.left - gridRect.left;
      const top = startRect.top - gridRect.top;
      const width = endRect.right - startRect.left;
      const height = endRect.bottom - startRect.top;

      overlay.style.left = left + 'px';
      overlay.style.top = top + 'px';
      overlay.style.width = width + 'px';
      overlay.style.height = height + 'px';

      // Add to grid container so it's positioned relative to it
      gridContainer.appendChild(overlay);
    }
  }

  handleGlobalKeydown(e) {
    // Only handle copy/paste/delete when not typing in an input field (except current cell)
    if (e.target.tagName === 'INPUT' && e.target !== this.currentCell) {
      return;
    }

    if ((e.ctrlKey || e.metaKey)) {
      switch (e.key.toLowerCase()) {
        case 'c':
          e.preventDefault();
          this.copySelection();
          break;
        case 'v':
          e.preventDefault();
          this.pasteSelection();
          break;
        case 'x':
          e.preventDefault();
          this.cutSelection();
          break;
      }
    } else {
      // Handle delete/backspace for multi-cell selections and escape for copy selection
      switch (e.key) {
        case 'Backspace':
        case 'Delete':
          if (this.selectedCells.size > 0) {
            e.preventDefault();
            this.clearSelectedCells();
          }
          break;
        case 'Escape':
          e.preventDefault();
          this.clearCopySelection();
          break;
      }
    }
  }

  copySelection() {
    if (this.selectedCells.size === 0 && this.currentCell) {
      // Copy single selected cell
      const cellId = this.currentCell.dataset.cellId;
      this.copiedData = [[this.data[cellId] || '']];
      this.copiedRange = { rows: 1, cols: 1 };
      this.copyToClipboard(this.copiedData);
      this.showCopiedFeedback();
      console.log('Copied single cell:', cellId);
    } else if (this.selectedCells.size > 0) {
      // Copy multi-cell selection
      this.copyMultiCellSelection();
    }
  }

  copyMultiCellSelection() {
    if (!this.selectionStart || !this.selectionEnd) return;

    const startRow = Math.min(this.selectionStart.row, this.selectionEnd.row);
    const endRow = Math.max(this.selectionStart.row, this.selectionEnd.row);
    const startCol = Math.min(this.selectionStart.col, this.selectionEnd.col);
    const endCol = Math.max(this.selectionStart.col, this.selectionEnd.col);

    const copiedData = [];
    for (let row = startRow; row <= endRow; row++) {
      const rowData = [];
      for (let col = startCol; col <= endCol; col++) {
        const cellId = this.getCellId(row, col);
        rowData.push(this.data[cellId] || '');
      }
      copiedData.push(rowData);
    }

    this.copiedData = copiedData;
    this.copiedRange = {
      rows: endRow - startRow + 1,
      cols: endCol - startCol + 1
    };

    // Copy to system clipboard
    this.copyToClipboard(copiedData);

    // Add visual feedback for copied cells
    this.showCopiedFeedback();
    console.log('Copied', this.copiedRange.rows, 'x', this.copiedRange.cols, 'cells');
  }

  cutSelection() {
    this.copySelection();

    if (this.selectedCells.size === 0 && this.currentCell) {
      // Cut single cell
      const cellId = this.currentCell.dataset.cellId;
      this.data[cellId] = '';
      this.currentCell.value = '';
    } else if (this.selectedCells.size > 0) {
      // Cut multi-cell selection
      this.selectedCells.forEach(cellId => {
        this.data[cellId] = '';
        const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
        if (cell) {
          cell.value = '';
        }
      });
    }

    // Save to localStorage
    this.saveToLocalStorage();
  }

  pasteSelection() {
    if (!this.copiedData) return;

    let targetRow, targetCol;

    if (this.currentCell) {
      // Paste starting from current cell
      targetRow = parseInt(this.currentCell.dataset.row);
      targetCol = parseInt(this.currentCell.dataset.col);
    } else if (this.selectionStart) {
      // Paste starting from selection start
      targetRow = this.selectionStart.row;
      targetCol = this.selectionStart.col;
    } else {
      return;
    }

    // Paste the data
    for (let row = 0; row < this.copiedData.length; row++) {
      for (let col = 0; col < this.copiedData[row].length; col++) {
        const pasteRow = targetRow + row;
        const pasteCol = targetCol + col;

        // Check bounds
        if (pasteRow >= this.rows || pasteCol >= this.cols) continue;

        const cellId = this.getCellId(pasteRow, pasteCol);
        const value = this.copiedData[row][col];

        // Update data
        this.data[cellId] = value;

        // Update UI
        const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
        if (cell) {
          cell.value = value;
        }
      }
    }

    // Update cell editor if current cell was pasted to
    if (this.currentCell) {
      const cellInput = document.getElementById('cell-input');
      cellInput.value = this.currentCell.value;
    }

    // Save to localStorage
    this.saveToLocalStorage();

    // Clear copied feedback after pasting
    this.clearCopiedFeedback();

    console.log('Pasted', this.copiedRange.rows, 'x', this.copiedRange.cols, 'cells');
  }

  showCopiedFeedback() {
    // Clear any existing copied feedback
    this.clearCopiedFeedback();

    if (this.selectedCells.size === 0 && this.currentCell) {
      // Single cell copied - show dashed border on individual cell
      this.currentCell.classList.add('copied');
    } else if (this.selectedCells.size > 0) {
      // Multi-cell selection copied - only show overlay border, not individual cell borders
      this.createCopiedOverlay();
    }
  }

  clearCopiedFeedback() {
    // Remove copied class from all cells
    document.querySelectorAll('.cell.copied').forEach(cell => {
      cell.classList.remove('copied');
    });

    // Remove copied overlay
    const copiedOverlay = document.querySelector('.copied-overlay');
    if (copiedOverlay) {
      copiedOverlay.remove();
    }
  }

  createCopiedOverlay() {
    if (!this.selectionStart || !this.selectionEnd) return;

    const startRow = Math.min(this.selectionStart.row, this.selectionEnd.row);
    const endRow = Math.max(this.selectionStart.row, this.selectionEnd.row);
    const startCol = Math.min(this.selectionStart.col, this.selectionEnd.col);
    const endCol = Math.max(this.selectionStart.col, this.selectionEnd.col);

    const gridContainer = document.querySelector('.grid-container');
    if (!gridContainer) return;

    const overlay = document.createElement('div');
    overlay.className = 'copied-overlay';

    // Get the actual cell containers
    const startCellContainer = document.querySelector(`[data-row="${startRow}"][data-col="${startCol}"]`).parentElement;
    const endCellContainer = document.querySelector(`[data-row="${endRow}"][data-col="${endCol}"]`).parentElement;

    if (startCellContainer && endCellContainer) {
      // Calculate positions relative to the grid container
      const gridRect = gridContainer.getBoundingClientRect();
      const startRect = startCellContainer.getBoundingClientRect();
      const endRect = endCellContainer.getBoundingClientRect();

      // Position relative to grid container
      const left = startRect.left - gridRect.left;
      const top = startRect.top - gridRect.top;
      const width = endRect.right - startRect.left;
      const height = endRect.bottom - startRect.top;

      overlay.style.left = left + 'px';
      overlay.style.top = top + 'px';
      overlay.style.width = width + 'px';
      overlay.style.height = height + 'px';

      gridContainer.appendChild(overlay);
    }
  }

  clearSelectedCells() {
    if (this.selectedCells.size === 0) return;

    // Clear all selected cells
    this.selectedCells.forEach(cellId => {
      this.data[cellId] = '';
      const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
      if (cell) {
        cell.value = '';
      }
    });

    // Update cell editor if one of the cleared cells was the current cell
    if (this.currentCell && this.selectedCells.has(this.currentCell.dataset.cellId)) {
      const cellInput = document.getElementById('cell-input');
      cellInput.value = '';
    }

    // Save to localStorage
    this.saveToLocalStorage();

    console.log('Cleared', this.selectedCells.size, 'selected cells');
  }

  clearCopySelection() {
    // Clear the copied data
    this.copiedData = null;
    this.copiedRange = null;

    // Remove all copy visual feedback
    this.clearCopiedFeedback();

    console.log('Cleared copy selection');
  }

  saveToLocalStorage() {
    if (!this.autoSaveEnabled) return;

    try {
      const saveData = {
        version: '1.0',
        data: this.data,
        columnWidths: this.columnWidths,
        rowHeights: this.rowHeights,
        metadata: {
          rows: this.rows,
          cols: this.cols,
          savedAt: new Date().toISOString(),
          autoSaved: true
        }
      };

      localStorage.setItem(this.storageKey, JSON.stringify(saveData));
      console.log('Auto-saved to localStorage');
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
      // Disable auto-save if localStorage is full or unavailable
      this.autoSaveEnabled = false;
    }
  }

  loadFromLocalStorage() {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (!savedData) {
        console.log('No saved data found in localStorage');
        return;
      }

      const parsedData = JSON.parse(savedData);

      // Load data
      if (parsedData.data) {
        this.data = { ...parsedData.data };
      }

      // Load layout information
      if (parsedData.columnWidths) {
        this.columnWidths = { ...parsedData.columnWidths };
      }

      if (parsedData.rowHeights) {
        this.rowHeights = { ...parsedData.rowHeights };
      }

      console.log('Loaded data from localStorage', parsedData.metadata?.savedAt || 'unknown time');
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
      // Clear corrupted data
      localStorage.removeItem(this.storageKey);
    }
  }

  populateGridFromData() {
    // Populate the grid with loaded data after DOM is ready
    setTimeout(() => {
      Object.keys(this.data).forEach(cellId => {
        const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
        if (cell && this.data[cellId]) {
          cell.value = this.data[cellId];
        }
      });

      // Apply layout settings
      this.applyLayoutSettings();

      console.log('Grid populated with', Object.keys(this.data).length, 'cells from localStorage');
    }, 100); // Small delay to ensure DOM is ready
  }

  clearLocalStorage() {
    localStorage.removeItem(this.storageKey);
    console.log('Cleared localStorage data');
  }

  async copyToClipboard(data) {
    if (!navigator.clipboard) {
      console.warn('Clipboard API not available');
      return;
    }

    try {
      // 1. Plain text format (tab-separated values)
      const plainText = this.formatAsPlainText(data);

      // 2. HTML table format
      const htmlTable = this.formatAsHTMLTable(data);

      // 3. CSV format
      const csvText = this.formatAsCSV(data);

      // Create clipboard item with multiple formats
      const clipboardItem = new ClipboardItem({
        'text/plain': new Blob([plainText], { type: 'text/plain' }),
        'text/html': new Blob([htmlTable], { type: 'text/html' }),
        'text/csv': new Blob([csvText], { type: 'text/csv' })
      });

      await navigator.clipboard.write([clipboardItem]);
      console.log('Copied to system clipboard in multiple formats');
    } catch (error) {
      console.warn('Failed to copy to clipboard:', error);
      // Fallback to simple text copy
      try {
        const plainText = this.formatAsPlainText(data);
        await navigator.clipboard.writeText(plainText);
        console.log('Copied to system clipboard as plain text');
      } catch (fallbackError) {
        console.warn('Clipboard copy failed completely:', fallbackError);
      }
    }
  }

  formatAsPlainText(data) {
    // Tab-separated values (standard for spreadsheet copy/paste)
    return data.map(row => row.join('\t')).join('\n');
  }

  formatAsCSV(data) {
    // CSV format with proper escaping
    return data.map(row => {
      return row.map(cell => {
        const value = cell || '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return '"' + value.replace(/"/g, '""') + '"';
        }
        return value;
      }).join(',');
    }).join('\n');
  }

  formatAsHTMLTable(data) {
    // HTML table format for rich paste into applications like Word, Outlook
    let html = '<table border="1" cellpadding="4" cellspacing="0">';

    data.forEach(row => {
      html += '<tr>';
      row.forEach(cell => {
        const value = (cell || '').replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        html += `<td>${value}</td>`;
      });
      html += '</tr>';
    });

    html += '</table>';
    return html;
  }
}

// Initialize the spreadsheet when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new SimpleSpreadsheet();
});
