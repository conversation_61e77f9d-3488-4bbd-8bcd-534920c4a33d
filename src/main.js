import './style.css'

class SimpleSpreadsheet {
  constructor() {
    this.data = {};
    this.currentCell = null;
    this.rows = 20;
    this.cols = 10;
    this.init();
  }

  init() {
    this.createGrid();
    this.setupEventListeners();
  }

  createGrid() {
    const gridContainer = document.getElementById('spreadsheet-grid');
    const table = document.createElement('table');
    table.className = 'grid-table';

    // Create header row with column letters
    const headerRow = document.createElement('tr');
    headerRow.appendChild(document.createElement('th')); // Empty corner cell

    for (let col = 0; col < this.cols; col++) {
      const th = document.createElement('th');
      th.textContent = String.fromCharCode(65 + col); // A, B, C, etc.
      headerRow.appendChild(th);
    }
    table.appendChild(headerRow);

    // Create data rows
    for (let row = 0; row < this.rows; row++) {
      const tr = document.createElement('tr');

      // Row header
      const rowHeader = document.createElement('th');
      rowHeader.className = 'row-header';
      rowHeader.textContent = (row + 1).toString();
      tr.appendChild(rowHeader);

      // Data cells
      for (let col = 0; col < this.cols; col++) {
        const td = document.createElement('td');
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'cell';
        input.dataset.row = row;
        input.dataset.col = col;
        input.dataset.cellId = this.getCellId(row, col);

        td.appendChild(input);
        tr.appendChild(td);
      }

      table.appendChild(tr);
    }

    gridContainer.appendChild(table);
  }

  getCellId(row, col) {
    return String.fromCharCode(65 + col) + (row + 1);
  }

  setupEventListeners() {
    const cellInput = document.getElementById('cell-input');
    const cells = document.querySelectorAll('.cell');

    // Handle cell focus and selection
    cells.forEach(cell => {
      cell.addEventListener('focus', (e) => {
        this.selectCell(e.target);
      });

      cell.addEventListener('input', (e) => {
        this.updateCellData(e.target);
      });

      cell.addEventListener('keydown', (e) => {
        this.handleKeyNavigation(e);
      });
    });

    // Handle cell editor input
    cellInput.addEventListener('input', (e) => {
      if (this.currentCell) {
        this.currentCell.value = e.target.value;
        this.updateCellData(this.currentCell);
      }
    });

    cellInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && this.currentCell) {
        this.currentCell.focus();
      }
    });
  }

  selectCell(cell) {
    // Remove previous selection
    document.querySelectorAll('.cell.selected').forEach(c => {
      c.classList.remove('selected');
    });

    // Select new cell
    cell.classList.add('selected');
    this.currentCell = cell;

    // Update cell editor
    const cellInput = document.getElementById('cell-input');
    cellInput.value = cell.value;
    cellInput.placeholder = `Editing ${cell.dataset.cellId}`;
  }

  updateCellData(cell) {
    const cellId = cell.dataset.cellId;
    this.data[cellId] = cell.value;
  }

  handleKeyNavigation(e) {
    if (!this.currentCell) return;

    const row = parseInt(this.currentCell.dataset.row);
    const col = parseInt(this.currentCell.dataset.col);
    let newRow = row;
    let newCol = col;

    switch (e.key) {
      case 'ArrowUp':
        newRow = Math.max(0, row - 1);
        e.preventDefault();
        break;
      case 'ArrowDown':
        newRow = Math.min(this.rows - 1, row + 1);
        e.preventDefault();
        break;
      case 'ArrowLeft':
        newCol = Math.max(0, col - 1);
        e.preventDefault();
        break;
      case 'ArrowRight':
        newCol = Math.min(this.cols - 1, col + 1);
        e.preventDefault();
        break;
      case 'Enter':
        newRow = Math.min(this.rows - 1, row + 1);
        e.preventDefault();
        break;
      case 'Tab':
        newCol = Math.min(this.cols - 1, col + 1);
        e.preventDefault();
        break;
    }

    if (newRow !== row || newCol !== col) {
      const newCell = document.querySelector(`[data-row="${newRow}"][data-col="${newCol}"]`);
      if (newCell) {
        newCell.focus();
      }
    }
  }
}

// Initialize the spreadsheet when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new SimpleSpreadsheet();
});
