import './style.css'

class SimpleSpreadsheet {
  constructor() {
    this.data = {};
    this.currentCell = null;
    this.rows = 20;
    this.cols = 10;
    this.columnWidths = {};
    this.rowHeights = {};
    this.init();
  }

  init() {
    this.createGrid();
    this.setupEventListeners();
  }

  createGrid() {
    const gridContainer = document.getElementById('spreadsheet-grid');
    const table = document.createElement('table');
    table.className = 'grid-table';

    // Create header row with column letters
    const headerRow = document.createElement('tr');
    headerRow.appendChild(document.createElement('th')); // Empty corner cell

    for (let col = 0; col < this.cols; col++) {
      const th = document.createElement('th');
      th.textContent = String.fromCharCode(65 + col); // A, B, C, etc.
      headerRow.appendChild(th);
    }
    table.appendChild(headerRow);

    // Create data rows
    for (let row = 0; row < this.rows; row++) {
      const tr = document.createElement('tr');

      // Row header
      const rowHeader = document.createElement('th');
      rowHeader.className = 'row-header';
      rowHeader.textContent = (row + 1).toString();
      tr.appendChild(rowHeader);

      // Data cells
      for (let col = 0; col < this.cols; col++) {
        const td = document.createElement('td');
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'cell';
        input.dataset.row = row;
        input.dataset.col = col;
        input.dataset.cellId = this.getCellId(row, col);

        td.appendChild(input);
        tr.appendChild(td);
      }

      table.appendChild(tr);
    }

    gridContainer.appendChild(table);
  }

  getCellId(row, col) {
    return String.fromCharCode(65 + col) + (row + 1);
  }

  setupEventListeners() {
    const cellInput = document.getElementById('cell-input');
    const cells = document.querySelectorAll('.cell');

    // Handle cell focus and selection
    cells.forEach(cell => {
      cell.addEventListener('focus', (e) => {
        this.selectCell(e.target);
      });

      cell.addEventListener('input', (e) => {
        this.updateCellData(e.target);
      });

      cell.addEventListener('keydown', (e) => {
        this.handleKeyNavigation(e);
      });
    });

    // Handle cell editor input
    cellInput.addEventListener('input', (e) => {
      if (this.currentCell) {
        this.currentCell.value = e.target.value;
        this.updateCellData(this.currentCell);
      }
    });

    cellInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && this.currentCell) {
        this.currentCell.focus();
      }
    });

    // File operation event listeners
    this.setupFileOperations();
  }

  setupFileOperations() {
    const saveCsvBtn = document.getElementById('save-csv');
    const loadCsvBtn = document.getElementById('load-csv');
    const saveJsonBtn = document.getElementById('save-json');
    const loadJsonBtn = document.getElementById('load-json');
    const fileInput = document.getElementById('file-input');

    saveCsvBtn.addEventListener('click', () => this.saveAsCSV());
    saveJsonBtn.addEventListener('click', () => this.saveAsJSON());

    loadCsvBtn.addEventListener('click', () => {
      fileInput.accept = '.csv';
      fileInput.onchange = (e) => this.loadCSV(e);
      fileInput.click();
    });

    loadJsonBtn.addEventListener('click', () => {
      fileInput.accept = '.json';
      fileInput.onchange = (e) => this.loadJSON(e);
      fileInput.click();
    });
  }

  selectCell(cell) {
    // Remove previous selection
    document.querySelectorAll('.cell.selected').forEach(c => {
      c.classList.remove('selected');
    });

    // Select new cell
    cell.classList.add('selected');
    this.currentCell = cell;

    // Update cell editor
    const cellInput = document.getElementById('cell-input');
    cellInput.value = cell.value;
    cellInput.placeholder = `Editing ${cell.dataset.cellId}`;
  }

  updateCellData(cell) {
    const cellId = cell.dataset.cellId;
    this.data[cellId] = cell.value;
  }

  handleKeyNavigation(e) {
    if (!this.currentCell) return;

    const row = parseInt(this.currentCell.dataset.row);
    const col = parseInt(this.currentCell.dataset.col);
    let newRow = row;
    let newCol = col;

    switch (e.key) {
      case 'ArrowUp':
        newRow = Math.max(0, row - 1);
        e.preventDefault();
        break;
      case 'ArrowDown':
        newRow = Math.min(this.rows - 1, row + 1);
        e.preventDefault();
        break;
      case 'ArrowLeft':
        newCol = Math.max(0, col - 1);
        e.preventDefault();
        break;
      case 'ArrowRight':
        newCol = Math.min(this.cols - 1, col + 1);
        e.preventDefault();
        break;
      case 'Enter':
        newRow = Math.min(this.rows - 1, row + 1);
        e.preventDefault();
        break;
      case 'Tab':
        newCol = Math.min(this.cols - 1, col + 1);
        e.preventDefault();
        break;
    }

    if (newRow !== row || newCol !== col) {
      const newCell = document.querySelector(`[data-row="${newRow}"][data-col="${newCol}"]`);
      if (newCell) {
        newCell.focus();
      }
    }
  }

  saveAsCSV() {
    const csvData = [];

    // Find the maximum row and column with data
    let maxRow = 0;
    let maxCol = 0;

    Object.keys(this.data).forEach(cellId => {
      if (this.data[cellId]) {
        const col = cellId.charCodeAt(0) - 65;
        const row = parseInt(cellId.substring(1)) - 1;
        maxRow = Math.max(maxRow, row);
        maxCol = Math.max(maxCol, col);
      }
    });

    // Generate CSV rows
    for (let row = 0; row <= maxRow; row++) {
      const csvRow = [];
      for (let col = 0; col <= maxCol; col++) {
        const cellId = this.getCellId(row, col);
        const value = this.data[cellId] || '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          csvRow.push('"' + value.replace(/"/g, '""') + '"');
        } else {
          csvRow.push(value);
        }
      }
      csvData.push(csvRow.join(','));
    }

    const csvContent = csvData.join('\n');
    this.downloadFile(csvContent, 'spreadsheet.csv', 'text/csv');
  }

  saveAsJSON() {
    const jsonData = {
      version: '1.0',
      data: this.data,
      columnWidths: this.columnWidths,
      rowHeights: this.rowHeights,
      metadata: {
        rows: this.rows,
        cols: this.cols,
        savedAt: new Date().toISOString()
      }
    };

    const jsonContent = JSON.stringify(jsonData, null, 2);
    this.downloadFile(jsonContent, 'spreadsheet.json', 'application/json');
  }

  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  loadCSV(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const csvContent = e.target.result;
      this.parseCSV(csvContent);
    };
    reader.readAsText(file);
  }

  parseCSV(csvContent) {
    // Clear existing data
    this.clearAllData();

    const lines = csvContent.split('\n');

    lines.forEach((line, rowIndex) => {
      if (line.trim() === '') return;

      const values = this.parseCSVLine(line);
      values.forEach((value, colIndex) => {
        if (value.trim() !== '') {
          const cellId = this.getCellId(rowIndex, colIndex);
          this.data[cellId] = value;

          // Update the cell in the UI
          const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
          if (cell) {
            cell.value = value;
          }
        }
      });
    });
  }

  parseCSVLine(line) {
    const values = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        values.push(current);
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    values.push(current);
    return values;
  }

  loadJSON(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result);
        this.parseJSON(jsonData);
      } catch (error) {
        alert('Error loading JSON file: ' + error.message);
      }
    };
    reader.readAsText(file);
  }

  parseJSON(jsonData) {
    // Clear existing data
    this.clearAllData();

    // Load data
    if (jsonData.data) {
      this.data = { ...jsonData.data };
    }

    // Load layout information
    if (jsonData.columnWidths) {
      this.columnWidths = { ...jsonData.columnWidths };
    }

    if (jsonData.rowHeights) {
      this.rowHeights = { ...jsonData.rowHeights };
    }

    // Update UI with loaded data
    Object.keys(this.data).forEach(cellId => {
      const cell = document.querySelector(`[data-cell-id="${cellId}"]`);
      if (cell) {
        cell.value = this.data[cellId];
      }
    });

    // Apply column widths and row heights
    this.applyLayoutSettings();
  }

  clearAllData() {
    this.data = {};
    this.columnWidths = {};
    this.rowHeights = {};

    // Clear all cells in the UI
    document.querySelectorAll('.cell').forEach(cell => {
      cell.value = '';
    });
  }

  applyLayoutSettings() {
    // Apply column widths
    Object.keys(this.columnWidths).forEach(col => {
      const colIndex = col.charCodeAt(0) - 65;
      const cells = document.querySelectorAll(`[data-col="${colIndex}"]`);
      cells.forEach(cell => {
        cell.parentElement.style.width = this.columnWidths[col] + 'px';
      });
    });

    // Apply row heights
    Object.keys(this.rowHeights).forEach(row => {
      const rowIndex = parseInt(row) - 1;
      const cells = document.querySelectorAll(`[data-row="${rowIndex}"]`);
      cells.forEach(cell => {
        cell.parentElement.style.height = this.rowHeights[row] + 'px';
      });
    });
  }
}

// Initialize the spreadsheet when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new SimpleSpreadsheet();
});
