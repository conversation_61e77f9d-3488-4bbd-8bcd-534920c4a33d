<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Spreadsheet</title>
  <style>
    body {
      font-family: system-ui, -apple-system, sans-serif;
      margin: 20px;
    }
    table {
      border-collapse: collapse;
    }
    td {
      border: 1px solid #ccc;
      width: 100px;
      height: 25px;
      text-align: left;
      padding: 3px;
    }
    .cell-editor {
      margin-top: 20px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    textarea {
      width: 300px;
      height: 80px;
    }
  </style>
</head>
<body>
  <h1>Simple Spreadsheet</h1>
  
  <table id="spreadsheet"></table>
  
  <div class="cell-editor">
    <label for="cell-content">Cell Content:</label>
    <textarea id="cell-content" placeholder="Edit cell content here"></textarea>
  </div>

  <script>
    // Create a 10x10 grid
    const rows = 10;
    const cols = 10;
    const spreadsheet = document.getElementById('spreadsheet');
    const cellContent = document.getElementById('cell-content');
    let activeCell = null;
    
    // Initialize the spreadsheet grid
    function initGrid() {
      for (let i = 0; i < rows; i++) {
        const row = spreadsheet.insertRow();
        for (let j = 0; j < cols; j++) {
          const cell = row.insertCell();
          cell.dataset.row = i;
          cell.dataset.col = j;
          cell.addEventListener('click', () => selectCell(cell));
        }
      }
    }
    
    // Handle cell selection
    function selectCell(cell) {
      if (activeCell) {
        activeCell.style.backgroundColor = '';
      }
      
      activeCell = cell;
      activeCell.style.backgroundColor = '#e0e0e0';
      cellContent.value = activeCell.textContent;
      cellContent.focus();
    }
    
    // Update cell content when textarea changes
    cellContent.addEventListener('input', () => {
      if (activeCell) {
        activeCell.textContent = cellContent.value;
      }
    });
    
    // Initialize the grid
    initGrid();
  </script>
</body>
</html>